"""
Pokemon Showdown Process Manager

Manages Pokemon Showdown simulator processes, handles process lifecycle,
and provides communication interfaces.
"""

import asyncio
import json
import logging
import os
import uuid
from typing import Dict, Optional, Callable, Any
from pathlib import Path

from ..config import config
from ..models import BattleFormat

logger = logging.getLogger(__name__)


class ShowdownProcess:
    """Represents a single Pokemon Showdown simulator process"""
    
    def __init__(self, process_id: str):
        self.process_id = process_id
        self.process: Optional[asyncio.subprocess.Process] = None
        self.is_running = False
        self.battles: Dict[str, str] = {}  # battle_id -> showdown_battle_id
        self.message_handlers: Dict[str, Callable] = {}
        
    async def start(self) -> bool:
        """Start the Pokemon Showdown process"""
        try:
            showdown_path = Path(config.showdown_path)
            if not showdown_path.exists():
                logger.error(f"Pokemon Showdown path not found: {showdown_path}")
                return False
                
            # Start the Pokemon Showdown simulator process
            cmd = [
                config.showdown_node_path,
                str(showdown_path / "pokemon-showdown"),
                "simulate-battle"
            ]
            
            self.process = await asyncio.create_subprocess_exec(
                *cmd,
                stdin=asyncio.subprocess.PIPE,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=str(showdown_path)
            )
            
            self.is_running = True
            logger.info(f"Started Showdown process {self.process_id} with PID {self.process.pid}")
            
            # Start output monitoring
            asyncio.create_task(self._monitor_output())
            asyncio.create_task(self._monitor_errors())
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to start Showdown process {self.process_id}: {e}")
            return False
    
    async def stop(self):
        """Stop the Pokemon Showdown process"""
        if self.process and self.is_running:
            try:
                self.process.terminate()
                await asyncio.wait_for(self.process.wait(), timeout=5.0)
            except asyncio.TimeoutError:
                logger.warning(f"Force killing Showdown process {self.process_id}")
                self.process.kill()
                await self.process.wait()
            
            self.is_running = False
            logger.info(f"Stopped Showdown process {self.process_id}")
    
    async def send_command(self, command: str) -> bool:
        """Send a command to the Pokemon Showdown process"""
        if not self.process or not self.is_running:
            logger.error(f"Process {self.process_id} is not running")
            return False
            
        try:
            if not command.endswith('\n'):
                command += '\n'
            
            if config.debug_mode:
                logger.debug(f"Sending to {self.process_id}: {command.strip()}")
                
            self.process.stdin.write(command.encode())
            await self.process.stdin.drain()
            return True
            
        except Exception as e:
            logger.error(f"Failed to send command to {self.process_id}: {e}")
            return False
    
    async def _monitor_output(self):
        """Monitor stdout from the Pokemon Showdown process"""
        if not self.process:
            return
            
        try:
            while self.is_running and self.process.stdout:
                line = await self.process.stdout.readline()
                if not line:
                    break
                    
                line_str = line.decode().strip()
                if line_str:
                    if config.log_showdown_output:
                        logger.debug(f"Showdown {self.process_id} output: {line_str}")
                    
                    # Handle the output message
                    await self._handle_output_message(line_str)
                    
        except Exception as e:
            logger.error(f"Error monitoring output for {self.process_id}: {e}")
    
    async def _monitor_errors(self):
        """Monitor stderr from the Pokemon Showdown process"""
        if not self.process:
            return
            
        try:
            while self.is_running and self.process.stderr:
                line = await self.process.stderr.readline()
                if not line:
                    break
                    
                line_str = line.decode().strip()
                if line_str:
                    logger.warning(f"Showdown {self.process_id} error: {line_str}")
                    
        except Exception as e:
            logger.error(f"Error monitoring stderr for {self.process_id}: {e}")
    
    async def _handle_output_message(self, message: str):
        """Handle output message from Pokemon Showdown"""
        # Parse the message and route to appropriate handlers
        for handler in self.message_handlers.values():
            try:
                await handler(message)
            except Exception as e:
                logger.error(f"Error in message handler: {e}")
    
    def add_message_handler(self, handler_id: str, handler: Callable):
        """Add a message handler"""
        self.message_handlers[handler_id] = handler
    
    def remove_message_handler(self, handler_id: str):
        """Remove a message handler"""
        self.message_handlers.pop(handler_id, None)


class ShowdownProcessManager:
    """Manages multiple Pokemon Showdown processes"""
    
    def __init__(self):
        self.processes: Dict[str, ShowdownProcess] = {}
        self.battle_to_process: Dict[str, str] = {}  # battle_id -> process_id
        self._lock = asyncio.Lock()
    
    async def get_available_process(self) -> Optional[ShowdownProcess]:
        """Get an available Pokemon Showdown process"""
        async with self._lock:
            # Find a process with fewer battles
            best_process = None
            min_battles = float('inf')
            
            for process in self.processes.values():
                if process.is_running and len(process.battles) < min_battles:
                    best_process = process
                    min_battles = len(process.battles)
            
            # If no process available or all are busy, create a new one
            if not best_process or min_battles >= 5:  # Max 5 battles per process
                if len(self.processes) < config.max_concurrent_battles:
                    process_id = str(uuid.uuid4())
                    new_process = ShowdownProcess(process_id)
                    
                    if await new_process.start():
                        self.processes[process_id] = new_process
                        return new_process
                    else:
                        logger.error(f"Failed to start new Showdown process")
                        return None
            
            return best_process
    
    async def assign_battle_to_process(self, battle_id: str) -> Optional[str]:
        """Assign a battle to an available process"""
        process = await self.get_available_process()
        if process:
            self.battle_to_process[battle_id] = process.process_id
            return process.process_id
        return None
    
    def get_process_for_battle(self, battle_id: str) -> Optional[ShowdownProcess]:
        """Get the process handling a specific battle"""
        process_id = self.battle_to_process.get(battle_id)
        if process_id:
            return self.processes.get(process_id)
        return None
    
    async def remove_battle(self, battle_id: str):
        """Remove a battle from process tracking"""
        process_id = self.battle_to_process.pop(battle_id, None)
        if process_id and process_id in self.processes:
            process = self.processes[process_id]
            process.battles.pop(battle_id, None)
    
    async def shutdown_all(self):
        """Shutdown all Pokemon Showdown processes"""
        async with self._lock:
            for process in self.processes.values():
                await process.stop()
            self.processes.clear()
            self.battle_to_process.clear()
            logger.info("All Showdown processes shut down")
