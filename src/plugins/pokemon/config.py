"""
Configuration for Pokemon Plugin
"""

from pydantic import BaseModel
from typing import Optional
import os


class PokemonConfig(BaseModel):
    """Pokemon plugin configuration"""
    
    # Pokemon Showdown paths
    showdown_path: str = "./pokemon-showdown"
    showdown_node_path: str = "node"
    
    # Battle settings
    default_format: str = "gen9ou"
    max_concurrent_battles: int = 10
    battle_timeout: int = 1800  # 30 minutes
    
    # Database settings
    cache_pokemon_data: bool = True
    data_update_interval: int = 86400  # 24 hours
    
    # Debug settings
    debug_mode: bool = False
    log_showdown_output: bool = False
    
    # Nonebot settings
    command_prefix: str = "/pokemon"
    admin_users: list[str] = []
    
    @classmethod
    def from_env(cls) -> "PokemonConfig":
        """Load configuration from environment variables"""
        return cls(
            showdown_path=os.getenv("POKEMON_SHOWDOWN_PATH", "./pokemon-showdown"),
            showdown_node_path=os.getenv("POKEMON_NODE_PATH", "node"),
            default_format=os.getenv("POKEMON_DEFAULT_FORMAT", "gen9ou"),
            max_concurrent_battles=int(os.getenv("POKEMON_MAX_BATTLES", "10")),
            battle_timeout=int(os.getenv("POKEMON_BATTLE_TIMEOUT", "1800")),
            cache_pokemon_data=bool(os.getenv("POKEMON_CACHE_DATA", "true").lower() == "true"),
            data_update_interval=int(os.getenv("POKEMON_DATA_UPDATE_INTERVAL", "86400")),
            debug_mode=bool(os.getenv("POKEMON_DEBUG", "false").lower() == "true"),
            log_showdown_output=bool(os.getenv("POKEMON_LOG_SHOWDOWN", "false").lower() == "true"),
            command_prefix=os.getenv("POKEMON_COMMAND_PREFIX", "/pokemon"),
            admin_users=os.getenv("POKEMON_ADMIN_USERS", "").split(",") if os.getenv("POKEMON_ADMIN_USERS") else [],
        )


# Global configuration instance
config = PokemonConfig.from_env()
